import 'dart:async';
import 'dart:convert';
import 'package:eventflux/eventflux.dart';
import '../widgets/time_period_selector.dart';
import '../constants/url_form.dart';
import 'package:provider/provider.dart';
import '../constants/constants.dart';
import '../providers/auth_provider.dart';
import '../utils/app_logger.dart';

/// SSE响应数据模型
class SSEResponseData {
  final String? content;
  final String? error;
  final bool? isComplete; // 标识流是否完成

  SSEResponseData({
    this.content,
    this.error,
    this.isComplete,
  });

  factory SSEResponseData.fromJson(Map<String, dynamic> json) {
    return SSEResponseData(
      content: json['content'],
      error: json['error'],
      isComplete: json['isComplete'] ?? false,
    );
  }

  bool get hasError => error != null && error!.isNotEmpty;
  bool get hasContent => content != null && content!.isNotEmpty;
  bool get isStreamComplete => isComplete == true;
}

/// 健康报告服务类
/// 负责通过SSE接口获取健康分析报告数据
class HealthReportService {
  // 超时配置
  static const Duration _responseTimeout = Duration(seconds: 60);

  /// 当前活跃的EventFlux连接
  EventFlux? _currentEventFlux;

  /// 获取健康报告数据流
  /// [deviceName] 设备名称
  /// [period] 时间段类型
  /// [date] 选择的日期
  Stream<SSEResponseData> getHealthReportStream({
    required String deviceName,
    required TimePeriod period,
    required DateTime date,
  }) {
    final controller = StreamController<SSEResponseData>();
    Timer? timeoutTimer;
    EventFlux? eventFlux;

    // 异步执行，避免阻塞主线程
    Future.microtask(() async {
      try {
        AppLogger.info(
            '开始获取健康报告: 设备=$deviceName, 时间段=${period.name}, 日期=${date.toIso8601String().split('T').first}');

        // 取消之前的连接
        await cancelCurrentRequest();

        // 获取JWT token
        String? accessToken;
        try {
          final context = navigatorKey.currentContext;
          if (context == null) {
            if (!controller.isClosed) {
              controller.addError('应用上下文不可用，请重试');
              controller.close();
            }
            return;
          }

          final authProvider =
              Provider.of<AuthProvider>(context, listen: false);
          accessToken = authProvider.user?.accessToken;
        } catch (e) {
          // 测试环境中使用模拟token
          accessToken = 'test_token';
          AppLogger.warning('使用测试token: $e');
        }

        if (accessToken == null || accessToken.isEmpty) {
          if (!controller.isClosed) {
            controller.addError('用户未登录，请重新登录');
            controller.close();
          }
          return;
        }

        // 构建请求体
        final requestBody = {
          'device_name': deviceName,
          'period': period.name,
          'date': date.toIso8601String().split('T').first,
        };

        // 构建请求头
        final headers = {
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        };

        final url = UrlFormat.HealthReport;

        AppLogger.info('健康报告SSE请求URL: $url');
        AppLogger.info('请求体: ${jsonEncode(requestBody)}');

        // 设置响应超时（只在没有收到任何数据时触发）
        bool hasReceivedData = false;
        timeoutTimer = Timer(_responseTimeout, () {
          if (!hasReceivedData) {
            AppLogger.warning('健康报告SSE响应超时');
            if (!controller.isClosed) {
              controller.addError('请求超时，请检查网络连接后重试');
              controller.close();
            }
            eventFlux?.disconnect();
          }
        });

        // 创建EventFlux实例
        eventFlux = EventFlux.spawn();

        // 连接到SSE流
        eventFlux!.connect(
          EventFluxConnectionType.post,
          url,
          header: headers,
          body: requestBody,
          tag: 'HealthReportSSE',
          logReceivedData: false,
          onSuccessCallback: (EventFluxResponse? response) {
            AppLogger.info('健康报告EventFlux连接成功');

            // 监听数据流
            response?.stream?.listen(
              (EventFluxData data) {
                try {
                  AppLogger.info('接收到健康报告SSE事件: ${data.data}');

                  // 解析SSE数据
                  if (data.data.isNotEmpty) {
                    hasReceivedData = true; // 标记已收到数据
                    final jsonData = jsonDecode(data.data);
                    final responseData = SSEResponseData.fromJson(jsonData);

                    if (!controller.isClosed) {
                      controller.add(responseData);
                    }
                  }
                } catch (e, stackTrace) {
                  AppLogger.error('解析健康报告SSE数据失败: $e',
                      error: e, stackTrace: stackTrace);
                  if (!controller.isClosed) {
                    controller.addError('数据解析错误，请重试');
                  }
                }
              },
              onError: (error) {
                AppLogger.error('健康报告SSE数据流错误: $error', error: error);
                timeoutTimer?.cancel();
                if (!controller.isClosed) {
                  controller.addError('数据流错误，请重试');
                }
              },
              onDone: () {
                AppLogger.info('健康报告SSE数据流结束');
                timeoutTimer?.cancel();
                if (!controller.isClosed) {
                  controller.close();
                }
              },
            );
          },
          onError: (EventFluxException? error) {
            AppLogger.error('健康报告EventFlux连接错误: ${error?.message}',
                error: error ?? 'Unknown error');
            timeoutTimer?.cancel();
            if (!controller.isClosed) {
              controller.addError('网络连接错误，请检查网络后重试');
            }
          },
          onConnectionClose: () {
            AppLogger.info('健康报告EventFlux连接关闭');
            timeoutTimer?.cancel();
            if (!controller.isClosed) {
              controller.close();
            }
          },
        );

        // 保存当前连接引用
        _currentEventFlux = eventFlux;
      } catch (e, stackTrace) {
        AppLogger.error('创建健康报告SSE连接失败: $e', error: e, stackTrace: stackTrace);
        timeoutTimer?.cancel();
        if (!controller.isClosed) {
          controller.addError('连接失败，请检查网络设置');
        }
      }
    });

    // 当控制器关闭时清理资源
    controller.onCancel = () {
      timeoutTimer?.cancel();
      eventFlux?.disconnect();
      _currentEventFlux = null;
    };

    return controller.stream;
  }

  /// 取消当前请求
  Future<void> cancelCurrentRequest() async {
    if (_currentEventFlux != null) {
      AppLogger.info('取消当前健康报告EventFlux连接');
      await _currentEventFlux!.disconnect();
      _currentEventFlux = null;
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    await cancelCurrentRequest();
  }
}
